// Patent service for managing patent information and operations
export const patentService = {
  // Get patent details by ID
  async getPatentDetails(patentId) {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      // For now, return mock data
      return {
        id: patentId,
        name: '一种新型智能手机充电技术',
        number: 'CN202410001234.5',
        category: '电子技术',
        price: '50000',
        transferPrice: '50000',
        abstract: '本发明提供了一种新型的智能手机无线充电技术，能够大幅提升充电效率并减少能耗。该技术采用先进的磁共振原理，实现了远距离无线充电的突破。',
        applicationDate: '2024-01-15',
        expiryDate: '2044-01-15',
        ownerName: '张三',
        ownerIdNumber: '110101199001011234',
        uploaderAddress: '0x1234567890123456789012345678901234567890',
        uploaderName: '张三',
        uploaderPhone: '13800138000',
        isProxySale: false,
        status: 'normal', // normal, trading, under_review, under_protection
        documentHash: 'QmXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX',
        certificateHash: 'QmYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYY',
        uploadDate: '2024-01-15',
        viewCount: 156,
        downloadCount: 23,
        blockchain: {
          transactionHash: '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890',
          blockNumber: '12345678',
          contractAddress: '0x9876543210987654321098765432109876543210'
        },
        documents: {
          patent: 'patent_document.pdf',
          proxy: 'proxy_authorization.pdf',
          certificate: 'patent_certificate.pdf'
        }
      }
    } catch (error) {
      console.error('获取专利详情失败:', error)
      throw new Error('获取专利详情失败')
    }
  },

  // Get patent transaction history
  async getPatentTransactionHistory(patentId) {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      return [
        {
          id: 1,
          type: 'upload',
          timestamp: '2024-01-15 10:30:00',
          participant: '0x1234567890123456789012345678901234567890',
          participantName: '张三',
          action: '上传专利',
          status: 'completed'
        },
        {
          id: 2,
          type: 'review',
          timestamp: '2024-01-16 14:20:00',
          participant: '0x2345678901234567890123456789012345678901',
          participantName: '审核员A',
          action: '审核通过',
          status: 'completed'
        }
      ]
    } catch (error) {
      console.error('获取专利交易历史失败:', error)
      throw new Error('获取专利交易历史失败')
    }
  },

  // Search patents
  async searchPatents(searchParams) {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      const mockPatents = [
        {
          id: '1',
          name: '一种新型智能手机充电技术',
          number: 'CN202410001234.5',
          category: '电子技术',
          price: '50000',
          status: 'normal',
          uploaderAddress: '0x1234567890123456789012345678901234567890'
        },
        {
          id: '2',
          name: '智能家居控制系统',
          number: 'CN202410001235.6',
          category: '智能控制',
          price: '80000',
          status: 'normal',
          uploaderAddress: '0x2345678901234567890123456789012345678901'
        }
      ]

      // Simple filtering based on search params
      let filteredPatents = mockPatents

      if (searchParams.name) {
        filteredPatents = filteredPatents.filter(patent =>
          patent.name.includes(searchParams.name)
        )
      }

      if (searchParams.number) {
        filteredPatents = filteredPatents.filter(patent =>
          patent.number.includes(searchParams.number)
        )
      }

      if (searchParams.category) {
        filteredPatents = filteredPatents.filter(patent =>
          patent.category.includes(searchParams.category)
        )
      }

      return {
        patents: filteredPatents,
        total: filteredPatents.length,
        page: searchParams.page || 1,
        pageSize: searchParams.pageSize || 10
      }
    } catch (error) {
      console.error('搜索专利失败:', error)
      throw new Error('搜索专利失败')
    }
  },

  // Download patent document
  async downloadPatentDocument(patentId, documentType = 'patent') {
    try {
      // TODO: Replace with actual IPFS download
      console.log(`下载专利文档: ${patentId}, 类型: ${documentType}`)

      // Simulate download
      const link = document.createElement('a')
      link.href = '#'
      link.download = `patent_${patentId}_${documentType}.pdf`
      link.click()

      return {
        success: true,
        message: '文档下载成功'
      }
    } catch (error) {
      console.error('下载专利文档失败:', error)
      throw new Error('下载专利文档失败')
    }
  },

  // Get user's uploaded patents
  async getUserUploadedPatents(userAddress) {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      return [
        {
          id: '1',
          name: '智能手机快速充电技术',
          number: 'CN202123456789.1',
          status: 'approved',
          uploadDate: '2024-01-15',
          price: '50000',
          viewCount: 156,
          downloadCount: 23
        },
        {
          id: '2',
          name: '环保汽车发动机设计',
          number: 'CN202123456790.2',
          status: 'under_review',
          uploadDate: '2024-01-18',
          price: '80000',
          viewCount: 45,
          downloadCount: 8
        }
      ]
    } catch (error) {
      console.error('获取用户上传专利失败:', error)
      throw new Error('获取用户上传专利失败')
    }
  },

  // Get user's purchased patents
  async getUserPurchasedPatents(userAddress) {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      return [
        {
          id: '3',
          name: '智能家居控制系统',
          number: 'CN202123456791.3',
          purchaseDate: '2024-01-20',
          purchasePrice: '60000',
          sellerAddress: '0x2345678901234567890123456789012345678901',
          sellerName: '李四',
          transactionHash: '0xdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abc'
        }
      ]
    } catch (error) {
      console.error('获取用户购买专利失败:', error)
      throw new Error('获取用户购买专利失败')
    }
  },

  // Withdraw patent (make it unavailable for trading)
  async withdrawPatent(patentId) {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      console.log('撤回专利:', patentId)

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000))

      return {
        success: true,
        message: '专利撤回成功'
      }
    } catch (error) {
      console.error('撤回专利失败:', error)
      throw new Error('撤回专利失败')
    }
  },

  // Freeze patent (temporarily make it non-tradable)
  async freezePatent(patentId) {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      console.log('冻结专利:', patentId)

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000))

      return {
        success: true,
        message: '专利冻结成功'
      }
    } catch (error) {
      console.error('冻结专利失败:', error)
      throw new Error('冻结专利失败')
    }
  },

  // Restore patent (make it tradable again)
  async restorePatent(patentId) {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      console.log('恢复专利:', patentId)

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000))

      return {
        success: true,
        message: '专利恢复成功'
      }
    } catch (error) {
      console.error('恢复专利失败:', error)
      throw new Error('恢复专利失败')
    }
  }
}
