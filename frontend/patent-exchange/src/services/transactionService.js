// Transaction service for managing patent transactions and rights protection
export const transactionService = {
  // Get pending transactions for review (reviewer view)
  async getPendingTransactions() {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      return [
        {
          id: 'tx_001',
          patentId: '1',
          patentName: '一种新型智能手机充电技术',
          patentNumber: 'CN202410001234.5',
          buyerAddress: '0x2345678901234567890123456789012345678901',
          buyerName: '李四',
          sellerAddress: '0x1234567890123456789012345678901234567890',
          sellerName: '张三',
          price: '50000',
          submitDate: '2024-01-20 10:30:00',
          status: 'pending',
          type: 'purchase'
        },
        {
          id: 'tx_002',
          patentId: '2',
          patentName: '智能家居控制系统',
          patentNumber: 'CN202410001235.6',
          buyerAddress: '0x3456789012345678901234567890123456789012',
          buyerName: '王五',
          sellerAddress: '0x2345678901234567890123456789012345678901',
          sellerName: '李四',
          price: '80000',
          submitDate: '2024-01-21 14:20:00',
          status: 'pending',
          type: 'purchase'
        }
      ]
    } catch (error) {
      console.error('获取待审核交易失败:', error)
      throw new Error('获取待审核交易失败')
    }
  },

  // Get user's transactions
  async getUserTransactions(userAddress) {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      return [
        {
          id: 'tx_003',
          patentId: '3',
          patentName: '人工智能图像识别算法',
          patentNumber: 'CN202410001236.7',
          buyerAddress: userAddress,
          buyerName: '当前用户',
          sellerAddress: '0x4567890123456789012345678901234567890123',
          sellerName: '赵六',
          price: '120000',
          submitDate: '2024-01-18 16:45:00',
          approvalDate: '2024-01-19 09:30:00',
          status: 'approved',
          type: 'purchase'
        }
      ]
    } catch (error) {
      console.error('获取用户交易记录失败:', error)
      throw new Error('获取用户交易记录失败')
    }
  },

  // Initiate patent transaction
  async initiateTransaction(transactionData) {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      console.log('发起专利交易:', transactionData)

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1500))

      return {
        success: true,
        transactionId: 'tx_' + Date.now(),
        message: '交易申请已提交，等待审核'
      }
    } catch (error) {
      console.error('发起交易失败:', error)
      throw new Error('发起交易失败')
    }
  },

  // Approve transaction (reviewer action)
  async approveTransaction(transactionId, reviewerAddress) {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      console.log('批准交易:', transactionId, '审核员:', reviewerAddress)

      await new Promise(resolve => setTimeout(resolve, 1000))

      return {
        success: true,
        message: '交易已批准'
      }
    } catch (error) {
      console.error('批准交易失败:', error)
      throw new Error('批准交易失败')
    }
  },

  // Reject transaction (reviewer action)
  async rejectTransaction(transactionId, reviewerAddress, reason) {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      console.log('拒绝交易:', transactionId, '审核员:', reviewerAddress, '原因:', reason)

      await new Promise(resolve => setTimeout(resolve, 1000))

      return {
        success: true,
        message: '交易已拒绝'
      }
    } catch (error) {
      console.error('拒绝交易失败:', error)
      throw new Error('拒绝交易失败')
    }
  },

  // Get pending patent uploads for review (reviewer view)
  async getPendingUploads() {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      return [
        {
          id: 'upload_001',
          patentName: '一种新型智能手机充电技术',
          patentNumber: 'CN202410001234.5',
          uploaderAddress: '0x1234567890123456789012345678901234567890',
          uploaderName: '张三',
          submitDate: '2024-01-20 10:30:00',
          transferPrice: '50000',
          patentAbstract: '本发明涉及一种新型智能手机充电技术，通过优化充电算法和硬件设计，实现快速、安全、高效的充电体验。该技术可以在30分钟内将手机电量从0%充至80%，同时保证电池寿命不受影响。',
          applicationDate: '2023-12-15',
          expirationDate: '2043-12-15',
          ownerName: '张三',
          ownerIdNumber: '110101199001011234',
          isAgentSale: true,
          status: 'pending',
          documentUrl: '/documents/patent_001.pdf',
          proxyDocumentUrl: '/documents/proxy_001.pdf'
        },
        {
          id: 'upload_002',
          patentName: '基于AI的图像识别算法',
          patentNumber: 'CN202410001235.6',
          uploaderAddress: '0x2345678901234567890123456789012345678901',
          uploaderName: '李四',
          submitDate: '2024-01-19 15:45:00',
          transferPrice: '120000',
          patentAbstract: '本发明提出了一种基于深度学习的图像识别算法，能够在复杂环境下准确识别多种目标对象，识别准确率达到99.5%以上。',
          applicationDate: '2023-11-20',
          expirationDate: '2043-11-20',
          ownerName: '李四',
          ownerIdNumber: '110101199002021234',
          isAgentSale: false,
          status: 'pending',
          documentUrl: '/documents/patent_002.pdf',
          proxyDocumentUrl: null
        }
      ]
    } catch (error) {
      console.error('获取待审核上传失败:', error)
      throw new Error('获取待审核上传失败')
    }
  },

  // Approve patent upload (reviewer action)
  async approveUpload(uploadId, reviewerAddress) {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      console.log('批准上传:', uploadId, '审核员:', reviewerAddress)

      await new Promise(resolve => setTimeout(resolve, 1000))

      return {
        success: true,
        message: '专利上传已批准'
      }
    } catch (error) {
      console.error('批准上传失败:', error)
      throw new Error('批准上传失败')
    }
  },

  // Reject patent upload (reviewer action)
  async rejectUpload(uploadId, reviewerAddress, reason) {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      console.log('拒绝上传:', uploadId, '审核员:', reviewerAddress, '原因:', reason)

      await new Promise(resolve => setTimeout(resolve, 1000))

      return {
        success: true,
        message: '专利上传已拒绝'
      }
    } catch (error) {
      console.error('拒绝上传失败:', error)
      throw new Error('拒绝上传失败')
    }
  },

  // Get pending rights protection requests for review (reviewer view)
  async getPendingProtectionRequests() {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      return [
        {
          id: 'protection_001',
          patentAddress: '0x1234567890123456789012345678901234567890',
          patentName: '一种新型智能手机充电技术',
          applicantAddress: '0x2345678901234567890123456789012345678901',
          applicantName: '王五',
          description: '发现某公司未经授权使用了我的专利技术，要求立即停止侵权行为并赔偿损失。该公司的产品与我的专利技术高度相似，存在明显的侵权行为。',
          submitDate: '2024-01-18 14:20:00',
          evidenceUrl: '/documents/evidence_001.pdf',
          status: 'pending'
        },
        {
          id: 'protection_002',
          patentAddress: '0x3456789012345678901234567890123456789012',
          patentName: '基于AI的图像识别算法',
          applicantAddress: '0x4567890123456789012345678901234567890123',
          applicantName: '赵六',
          description: '某互联网公司在其产品中使用了与我专利相同的算法，未经许可进行商业化应用，严重侵犯了我的专利权益。',
          submitDate: '2024-01-17 09:15:00',
          evidenceUrl: '/documents/evidence_002.pdf',
          status: 'pending'
        }
      ]
    } catch (error) {
      console.error('获取待审核维权申请失败:', error)
      throw new Error('获取待审核维权申请失败')
    }
  },

  // Approve rights protection request (reviewer action)
  async approveProtectionRequest(requestId, reviewerAddress) {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      console.log('批准维权申请:', requestId, '审核员:', reviewerAddress)

      await new Promise(resolve => setTimeout(resolve, 1000))

      return {
        success: true,
        message: '维权申请已批准'
      }
    } catch (error) {
      console.error('批准维权申请失败:', error)
      throw new Error('批准维权申请失败')
    }
  },

  // Reject rights protection request (reviewer action)
  async rejectProtectionRequest(requestId, reviewerAddress, reason) {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      console.log('拒绝维权申请:', requestId, '审核员:', reviewerAddress, '原因:', reason)

      await new Promise(resolve => setTimeout(resolve, 1000))

      return {
        success: true,
        message: '维权申请已拒绝'
      }
    } catch (error) {
      console.error('拒绝维权申请失败:', error)
      throw new Error('拒绝维权申请失败')
    }
  },

  // Get pending rights protection cases
  async getPendingProtectionCases() {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      return [
        {
          id: 'prot_001',
          patentId: '4',
          patentName: '区块链数据存储优化方案',
          patentNumber: 'CN202410001237.8',
          claimantAddress: '0x5678901234567890123456789012345678901234',
          claimantName: '孙七',
          currentOwnerAddress: '0x6789012345678901234567890123456789012345',
          currentOwnerName: '周八',
          description: '我是该专利的真正发明人，有充分的证据证明专利权归属。',
          evidenceHash: 'QmZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ',
          submitDate: '2024-01-22 11:15:00',
          status: 'pending'
        }
      ]
    } catch (error) {
      console.error('获取待审核维权案例失败:', error)
      throw new Error('获取待审核维权案例失败')
    }
  },

  // Initiate rights protection
  async initiateRightsProtection(protectionData) {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      console.log('发起专利维权:', protectionData)

      await new Promise(resolve => setTimeout(resolve, 1500))

      return {
        success: true,
        caseId: 'prot_' + Date.now(),
        message: '维权申请已提交，等待审核'
      }
    } catch (error) {
      console.error('发起维权失败:', error)
      throw new Error('发起维权失败')
    }
  },

  // Approve rights protection (reviewer action)
  async approveRightsProtection(caseId, reviewerAddress) {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      console.log('批准维权:', caseId, '审核员:', reviewerAddress)

      await new Promise(resolve => setTimeout(resolve, 1000))

      return {
        success: true,
        message: '维权申请已批准，专利权已转移'
      }
    } catch (error) {
      console.error('批准维权失败:', error)
      throw new Error('批准维权失败')
    }
  },

  // Reject rights protection (reviewer action)
  async rejectRightsProtection(caseId, reviewerAddress, reason) {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      console.log('拒绝维权:', caseId, '审核员:', reviewerAddress, '原因:', reason)

      await new Promise(resolve => setTimeout(resolve, 1000))

      return {
        success: true,
        message: '维权申请已拒绝'
      }
    } catch (error) {
      console.error('拒绝维权失败:', error)
      throw new Error('拒绝维权失败')
    }
  }
}
