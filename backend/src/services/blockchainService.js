const { Web3 } = require('web3');
const fs = require('fs');
const path = require('path');

class BlockchainService {
  constructor() {
    this.web3 = null;
    this.contracts = {};
    this.isInitialized = false;
    this.contractAddresses = {};
  }

  async initialize() {
    try {
      // Initialize Web3
      const ganacheUrl = process.env.GANACHE_URL || 'http://localhost:7545';
      this.web3 = new Web3(ganacheUrl);
      
      // Test connection
      await this.web3.eth.net.isListening();
      console.log('✅ Web3 connected to Ganache');
      
      // Load contract addresses
      await this.loadContractAddresses();
      
      // Load and initialize contracts
      await this.loadContracts();
      
      this.isInitialized = true;
      console.log('✅ Blockchain service initialized');
      
    } catch (error) {
      console.error('❌ Failed to initialize blockchain service:', error);
      throw error;
    }
  }

  async loadContractAddresses() {
    try {
      const addressesPath = path.join(__dirname, '../../contract-addresses.json');
      if (fs.existsSync(addressesPath)) {
        const addressesData = fs.readFileSync(addressesPath, 'utf8');
        this.contractAddresses = JSON.parse(addressesData);
        console.log('📄 Contract addresses loaded');
      } else {
        console.warn('⚠️ Contract addresses file not found. Please deploy contracts first.');
        this.contractAddresses = {};
      }
    } catch (error) {
      console.error('❌ Failed to load contract addresses:', error);
      this.contractAddresses = {};
    }
  }

  async loadContracts() {
    try {
      const contractNames = [
        'UserManagement',
        'PatentRegistry', 
        'TransactionManager',
        'ProtectionManager',
        'NotificationSystem'
      ];

      for (const contractName of contractNames) {
        await this.loadContract(contractName);
      }
      
      console.log('📄 All contracts loaded');
    } catch (error) {
      console.error('❌ Failed to load contracts:', error);
      throw error;
    }
  }

  async loadContract(contractName) {
    try {
      // Load ABI
      const abiPath = path.join(__dirname, `../../build/contracts/${contractName}.json`);
      if (!fs.existsSync(abiPath)) {
        console.warn(`⚠️ ABI file not found for ${contractName}. Please compile contracts first.`);
        return;
      }

      const contractData = JSON.parse(fs.readFileSync(abiPath, 'utf8'));
      const abi = contractData.abi;
      
      // Get contract address
      const address = this.contractAddresses[contractName];
      if (!address) {
        console.warn(`⚠️ Address not found for ${contractName}. Please deploy contracts first.`);
        return;
      }

      // Create contract instance
      this.contracts[contractName] = new this.web3.eth.Contract(abi, address);
      console.log(`✅ ${contractName} contract loaded at ${address}`);
      
    } catch (error) {
      console.error(`❌ Failed to load ${contractName} contract:`, error);
    }
  }

  isConnected() {
    return this.isInitialized && this.web3 !== null;
  }

  getContract(contractName) {
    if (!this.contracts[contractName]) {
      throw new Error(`Contract ${contractName} not loaded`);
    }
    return this.contracts[contractName];
  }

  async getAccounts() {
    if (!this.web3) throw new Error('Web3 not initialized');
    return await this.web3.eth.getAccounts();
  }

  async getBalance(address) {
    if (!this.web3) throw new Error('Web3 not initialized');
    const balance = await this.web3.eth.getBalance(address);
    return this.web3.utils.fromWei(balance, 'ether');
  }

  async sendTransaction(from, to, value, data = '0x') {
    if (!this.web3) throw new Error('Web3 not initialized');
    
    const gasPrice = await this.web3.eth.getGasPrice();
    const gasEstimate = await this.web3.eth.estimateGas({
      from,
      to,
      value,
      data
    });

    return await this.web3.eth.sendTransaction({
      from,
      to,
      value,
      data,
      gas: gasEstimate,
      gasPrice
    });
  }

  async callContractMethod(contractName, methodName, params = [], options = {}) {
    try {
      const contract = this.getContract(contractName);
      const method = contract.methods[methodName](...params);
      
      if (options.send) {
        // Send transaction (state-changing)
        const accounts = await this.getAccounts();
        const from = options.from || accounts[0];
        
        const gasEstimate = await method.estimateGas({ from });
        const gasPrice = await this.web3.eth.getGasPrice();
        
        return await method.send({
          from,
          gas: gasEstimate,
          gasPrice,
          ...options
        });
      } else {
        // Call method (read-only)
        return await method.call(options);
      }
    } catch (error) {
      console.error(`❌ Contract method call failed: ${contractName}.${methodName}`, error);
      throw error;
    }
  }

  async getTransactionReceipt(txHash) {
    if (!this.web3) throw new Error('Web3 not initialized');
    return await this.web3.eth.getTransactionReceipt(txHash);
  }

  async getCurrentBlock() {
    if (!this.web3) throw new Error('Web3 not initialized');
    return await this.web3.eth.getBlockNumber();
  }

  async getBlock(blockNumber) {
    if (!this.web3) throw new Error('Web3 not initialized');
    return await this.web3.eth.getBlock(blockNumber);
  }

  // Utility methods for address validation
  isValidAddress(address) {
    if (!this.web3) return false;
    return this.web3.utils.isAddress(address);
  }

  toChecksumAddress(address) {
    if (!this.web3) throw new Error('Web3 not initialized');
    return this.web3.utils.toChecksumAddress(address);
  }

  // Event listening
  async subscribeToEvents(contractName, eventName, callback, fromBlock = 'latest') {
    try {
      const contract = this.getContract(contractName);
      const subscription = contract.events[eventName]({
        fromBlock
      });
      
      subscription.on('data', callback);
      subscription.on('error', (error) => {
        console.error(`❌ Event subscription error: ${contractName}.${eventName}`, error);
      });
      
      return subscription;
    } catch (error) {
      console.error(`❌ Failed to subscribe to events: ${contractName}.${eventName}`, error);
      throw error;
    }
  }

  // Get past events
  async getPastEvents(contractName, eventName, options = {}) {
    try {
      const contract = this.getContract(contractName);
      return await contract.getPastEvents(eventName, {
        fromBlock: 0,
        toBlock: 'latest',
        ...options
      });
    } catch (error) {
      console.error(`❌ Failed to get past events: ${contractName}.${eventName}`, error);
      throw error;
    }
  }
}

// Create singleton instance
const blockchainService = new BlockchainService();

module.exports = blockchainService;
