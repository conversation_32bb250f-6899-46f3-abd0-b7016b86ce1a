/**
 * Role Service - Handles user role detection and management
 * Supports multiple role detection strategies:
 * 1. Smart contract-based role detection
 * 2. API-based role detection  
 * 3. Address-based role detection (for demo/testing)
 */

class RoleService {
  constructor() {
    // Configuration for role detection strategy
    this.strategy = 'address' // 'contract', 'api', 'address'
    this.contractAddress = null // Will be set when smart contract is deployed
    this.apiEndpoint = null // Will be set when backend API is available
    
    // Demo addresses for testing (can be modified)
    this.demoRoles = {
      // Admin addresses
      admin: [
        '0x8ba1f109551bd432803012645hac136c5c1e5f1e',
        '******************************************',
        '******************************************'
      ],
      // Reviewer addresses  
      reviewer: [
        '******************************************',
        '******************************************',
        '******************************************'
      ]
    }
  }

  /**
   * Get user role for a given address
   * @param {string} address - User's wallet address
   * @param {object} web3 - Web3 instance (optional, for smart contract calls)
   * @returns {Promise<string>} - User role ('admin', 'reviewer', 'user')
   */
  async getUserRole(address, web3 = null) {
    if (!address) {
      throw new Error('Address is required for role detection')
    }

    try {
      switch (this.strategy) {
        case 'contract':
          return await this.getRoleFromContract(address, web3)
        case 'api':
          return await this.getRoleFromAPI(address)
        case 'address':
        default:
          return this.getRoleFromAddress(address)
      }
    } catch (error) {
      console.error('Role detection failed:', error)
      throw error
    }
  }

  /**
   * Get role from smart contract
   * @param {string} address - User's wallet address
   * @param {object} web3 - Web3 instance
   * @returns {Promise<string>} - User role
   */
  async getRoleFromContract(address, web3) {
    if (!web3) {
      throw new Error('Web3 instance is required for contract-based role detection')
    }

    if (!this.contractAddress) {
      throw new Error('Contract address not configured')
    }

    try {
      // Example smart contract call (adjust based on your contract ABI)
      const contract = new web3.eth.Contract(
        [
          {
            "inputs": [{"name": "user", "type": "address"}],
            "name": "getUserRole",
            "outputs": [{"name": "", "type": "uint8"}],
            "stateMutability": "view",
            "type": "function"
          }
        ],
        this.contractAddress
      )

      const roleNumber = await contract.methods.getUserRole(address).call()
      
      // Convert role number to role string (adjust based on your contract)
      switch (parseInt(roleNumber)) {
        case 0: return 'user'
        case 1: return 'reviewer'
        case 2: return 'admin'
        default: return 'user'
      }
    } catch (error) {
      console.error('Smart contract role detection failed:', error)
      throw new Error('Failed to get role from smart contract')
    }
  }

  /**
   * Get role from backend API
   * @param {string} address - User's wallet address
   * @returns {Promise<string>} - User role
   */
  async getRoleFromAPI(address) {
    if (!this.apiEndpoint) {
      throw new Error('API endpoint not configured')
    }

    try {
      const response = await fetch(`${this.apiEndpoint}/user/role/${address}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`)
      }

      const data = await response.json()
      return data.role || 'user'
    } catch (error) {
      console.error('API role detection failed:', error)
      throw new Error('Failed to get role from API')
    }
  }

  /**
   * Get role based on address (for demo/testing purposes)
   * @param {string} address - User's wallet address
   * @returns {string} - User role
   */
  getRoleFromAddress(address) {
    const lowerAddress = address.toLowerCase()

    // Check admin addresses
    if (this.demoRoles.admin.some(addr => addr.toLowerCase() === lowerAddress)) {
      return 'admin'
    }

    // Check reviewer addresses
    if (this.demoRoles.reviewer.some(addr => addr.toLowerCase() === lowerAddress)) {
      return 'reviewer'
    }

    // Default to user role
    return 'user'
  }

  /**
   * Set role detection strategy
   * @param {string} strategy - 'contract', 'api', or 'address'
   * @param {object} config - Configuration object
   */
  setStrategy(strategy, config = {}) {
    this.strategy = strategy

    if (strategy === 'contract' && config.contractAddress) {
      this.contractAddress = config.contractAddress
    }

    if (strategy === 'api' && config.apiEndpoint) {
      this.apiEndpoint = config.apiEndpoint
    }

    console.log(`Role detection strategy set to: ${strategy}`)
  }

  /**
   * Add demo addresses for testing
   * @param {string} role - 'admin' or 'reviewer'
   * @param {string[]} addresses - Array of addresses
   */
  addDemoAddresses(role, addresses) {
    if (!this.demoRoles[role]) {
      this.demoRoles[role] = []
    }

    this.demoRoles[role].push(...addresses)
    console.log(`Added ${addresses.length} ${role} addresses for demo`)
  }

  /**
   * Get current configuration
   * @returns {object} - Current configuration
   */
  getConfig() {
    return {
      strategy: this.strategy,
      contractAddress: this.contractAddress,
      apiEndpoint: this.apiEndpoint,
      demoRoles: this.demoRoles
    }
  }

  /**
   * Validate if an address has a specific role
   * @param {string} address - User's wallet address
   * @param {string} requiredRole - Required role
   * @param {object} web3 - Web3 instance (optional)
   * @returns {Promise<boolean>} - Whether user has the required role
   */
  async hasRole(address, requiredRole, web3 = null) {
    try {
      const userRole = await this.getUserRole(address, web3)
      
      // Admin has access to all roles
      if (userRole === 'admin') {
        return true
      }

      // Exact role match
      return userRole === requiredRole
    } catch (error) {
      console.error('Role validation failed:', error)
      return false
    }
  }
}

// Create and export singleton instance
export const roleService = new RoleService()

// Export class for testing purposes
export { RoleService }
