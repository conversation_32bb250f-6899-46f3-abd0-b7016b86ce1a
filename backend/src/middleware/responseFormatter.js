/**
 * Response formatting middleware
 * Standardizes API responses across the application
 */

const responseFormatter = (req, res, next) => {
  // Success response helper
  res.success = (data, message = 'Success', statusCode = 200) => {
    const response = {
      success: true,
      message,
      data,
      timestamp: new Date().toISOString()
    };

    // Add pagination info if present
    if (data && data.pagination) {
      response.pagination = data.pagination;
      response.data = data.data || data;
      delete response.data.pagination;
    }

    return res.status(statusCode).json(response);
  };

  // Error response helper
  res.error = (message, statusCode = 500, code = 'INTERNAL_ERROR', details = {}) => {
    const response = {
      success: false,
      error: {
        code,
        message,
        details,
        timestamp: new Date().toISOString()
      }
    };

    // Add request info in development
    if (process.env.NODE_ENV === 'development') {
      response.error.request = {
        method: req.method,
        url: req.originalUrl,
        userAddress: req.userAddress
      };
    }

    return res.status(statusCode).json(response);
  };

  // Validation error helper
  res.validationError = (errors, message = 'Validation failed') => {
    return res.error(message, 400, 'VALIDATION_ERROR', { errors });
  };

  // Authentication error helper
  res.authError = (message = 'Authentication required') => {
    return res.error(message, 401, 'AUTHENTICATION_ERROR');
  };

  // Authorization error helper
  res.authzError = (message = 'Access denied') => {
    return res.error(message, 403, 'AUTHORIZATION_ERROR');
  };

  // Not found error helper
  res.notFound = (message = 'Resource not found') => {
    return res.error(message, 404, 'NOT_FOUND');
  };

  // Conflict error helper
  res.conflict = (message = 'Resource conflict') => {
    return res.error(message, 409, 'CONFLICT');
  };

  // Blockchain error helper
  res.blockchainError = (message = 'Blockchain operation failed') => {
    return res.error(message, 400, 'BLOCKCHAIN_ERROR');
  };

  // IPFS error helper
  res.ipfsError = (message = 'IPFS operation failed') => {
    return res.error(message, 503, 'IPFS_ERROR');
  };

  // Rate limit error helper
  res.rateLimitError = (message = 'Rate limit exceeded') => {
    return res.error(message, 429, 'RATE_LIMIT_EXCEEDED');
  };

  // Service unavailable error helper
  res.serviceUnavailable = (message = 'Service temporarily unavailable') => {
    return res.error(message, 503, 'SERVICE_UNAVAILABLE');
  };

  // Paginated response helper
  res.paginated = (data, pagination, message = 'Success') => {
    const response = {
      success: true,
      message,
      data,
      pagination,
      timestamp: new Date().toISOString()
    };

    return res.status(200).json(response);
  };

  // Created response helper
  res.created = (data, message = 'Resource created successfully') => {
    return res.success(data, message, 201);
  };

  // Updated response helper
  res.updated = (data, message = 'Resource updated successfully') => {
    return res.success(data, message, 200);
  };

  // Deleted response helper
  res.deleted = (message = 'Resource deleted successfully') => {
    return res.success(null, message, 200);
  };

  // No content response helper
  res.noContent = () => {
    return res.status(204).send();
  };

  next();
};

// Helper function to create pagination object
const createPagination = (page, limit, total) => {
  const totalPages = Math.ceil(total / limit);
  
  return {
    page: parseInt(page),
    limit: parseInt(limit),
    total: parseInt(total),
    totalPages,
    hasNext: page < totalPages,
    hasPrev: page > 1
  };
};

// Helper function to parse pagination parameters
const parsePaginationParams = (req) => {
  const page = Math.max(1, parseInt(req.query.page) || 1);
  const limit = Math.min(100, Math.max(1, parseInt(req.query.limit) || 20));
  const offset = (page - 1) * limit;
  
  return { page, limit, offset };
};

// Helper function to format blockchain data
const formatBlockchainData = (data) => {
  if (!data) return data;
  
  // Convert BigInt to string for JSON serialization
  const formatted = JSON.parse(JSON.stringify(data, (key, value) =>
    typeof value === 'bigint' ? value.toString() : value
  ));
  
  return formatted;
};

// Helper function to format dates
const formatDate = (timestamp) => {
  if (!timestamp) return null;
  
  // Handle both Unix timestamp (seconds) and JavaScript timestamp (milliseconds)
  const date = new Date(timestamp * (timestamp.toString().length === 10 ? 1000 : 1));
  return date.toISOString();
};

// Helper function to format address
const formatAddress = (address) => {
  if (!address || typeof address !== 'string') return address;
  
  // Return checksum address
  return address;
};

// Helper function to format file size
const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '0 B';
  
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  
  return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${sizes[i]}`;
};

module.exports = {
  responseFormatter,
  createPagination,
  parsePaginationParams,
  formatBlockchainData,
  formatDate,
  formatAddress,
  formatFileSize
};
