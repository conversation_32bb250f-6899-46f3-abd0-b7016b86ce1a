// User service for managing user information and profiles
export const userService = {
  // Get user profile information by blockchain address
  async getUserProfile(address) {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      // For now, return mock data
      return {
        address: address,
        name: '张三',
        phone: '13800138000',
        idNumber: '110101199001011234',
        registrationDate: '2024-01-15',
        lastLoginDate: '2024-01-20'
      }
    } catch (error) {
      console.error('获取用户资料失败:', error)
      throw new Error('获取用户资料失败')
    }
  },

  // Update user profile information
  async updateUserProfile(address, profileData) {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      console.log('更新用户资料:', address, profileData)

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000))

      return {
        success: true,
        message: '用户资料更新成功'
      }
    } catch (error) {
      console.error('更新用户资料失败:', error)
      throw new Error('更新用户资料失败')
    }
  },

  // Get user details for display in modals (when clicking blockchain addresses)
  async getUserDetails(address) {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      // For now, return mock data based on address
      const mockUsers = {
        '0x1234567890123456789012345678901234567890': {
          name: '李四',
          phone: '13900139000',
          idNumber: '110101199002021234',
          address: '0x1234567890123456789012345678901234567890'
        },
        '0x0987654321098765432109876543210987654321': {
          name: '王五',
          phone: '13700137000',
          idNumber: '110101199003031234',
          address: '0x0987654321098765432109876543210987654321'
        }
      }

      return mockUsers[address] || {
        name: '未知用户',
        phone: '未知',
        idNumber: '未知',
        address: address
      }
    } catch (error) {
      console.error('获取用户详情失败:', error)
      throw new Error('获取用户详情失败')
    }
  },

  // Get all users (admin only)
  async getAllUsers() {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      // For now, return mock data
      return [
        {
          address: '0x1234567890123456789012345678901234567890',
          name: '张三',
          phone: '13800138000',
          idNumber: '110101199001011234',
          role: 'user',
          registrationDate: '2024-01-15',
          lastLoginDate: '2024-01-20',
          status: 'active'
        },
        {
          address: '0x2345678901234567890123456789012345678901',
          name: '李四',
          phone: '13900139000',
          idNumber: '110101199002021234',
          role: 'reviewer',
          registrationDate: '2024-01-10',
          lastLoginDate: '2024-01-19',
          status: 'active'
        },
        {
          address: '0x3456789012345678901234567890123456789012',
          name: '王五',
          phone: '13700137000',
          idNumber: '110101199003031234',
          role: 'user',
          registrationDate: '2024-01-12',
          lastLoginDate: '2024-01-18',
          status: 'active'
        },
        {
          address: '0x4567890123456789012345678901234567890123',
          name: '赵六',
          phone: '13600136000',
          idNumber: '110101199004041234',
          role: 'user',
          registrationDate: '2024-01-08',
          lastLoginDate: '2024-01-17',
          status: 'inactive'
        }
      ]
    } catch (error) {
      console.error('获取用户列表失败:', error)
      throw new Error('获取用户列表失败')
    }
  },

  // Update user role (admin only)
  async updateUserRole(address, newRole) {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      console.log('更新用户角色:', address, newRole)

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000))

      return {
        success: true,
        message: '用户角色更新成功'
      }
    } catch (error) {
      console.error('更新用户角色失败:', error)
      throw new Error('更新用户角色失败')
    }
  },

  // Get user statistics (admin only)
  async getUserStatistics() {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      return {
        totalUsers: 156,
        activeUsers: 142,
        newUsersThisMonth: 23,
        usersByRole: {
          user: 134,
          reviewer: 18,
          admin: 4
        },
        userGrowth: [
          { month: '2023-09', count: 89 },
          { month: '2023-10', count: 102 },
          { month: '2023-11', count: 118 },
          { month: '2023-12', count: 133 },
          { month: '2024-01', count: 156 }
        ]
      }
    } catch (error) {
      console.error('获取用户统计失败:', error)
      throw new Error('获取用户统计失败')
    }
  },

  // Validate user profile data
  validateProfile(profileData) {
    const errors = []

    if (!profileData.name || profileData.name.trim().length < 2) {
      errors.push('姓名至少需要2个字符')
    }

    if (!profileData.phone || !/^1[3-9]\d{9}$/.test(profileData.phone)) {
      errors.push('请输入有效的手机号码')
    }

    if (!profileData.idNumber || !/^\d{17}[\dXx]$/.test(profileData.idNumber)) {
      errors.push('请输入有效的身份证号码')
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    }
  },

  // Validate role change
  validateRoleChange(currentRole, newRole, userRole) {
    const errors = []

    // Only admins can change roles
    if (userRole !== 'admin') {
      errors.push('只有管理员可以修改用户角色')
    }

    // Valid roles
    const validRoles = ['user', 'reviewer', 'admin']
    if (!validRoles.includes(newRole)) {
      errors.push('无效的角色类型')
    }

    // Cannot change own role
    if (currentRole === 'admin' && newRole !== 'admin') {
      errors.push('管理员不能降级自己的角色')
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    }
  }
}
