const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

class IPFSService {
  constructor() {
    this.client = null;
    this.isInitialized = false;
    this.gatewayUrl = process.env.IPFS_GATEWAY || 'http://localhost:8080';
  }

  async initialize() {
    try {
      const ipfsUrl = process.env.IPFS_URL || 'http://localhost:5001';
      this.apiUrl = ipfsUrl + '/api/v0';

      // Test connection by getting IPFS node info
      const response = await axios.post(`${this.apiUrl}/id`, null, {
        timeout: 10000
      });

      console.log('✅ IPFS connected, Node ID:', response.data.ID);

      this.isInitialized = true;
      console.log('✅ IPFS service initialized');

    } catch (error) {
      console.error('❌ Failed to initialize IPFS service:', error);
      console.warn('⚠️ IPFS service will operate in offline mode');
      this.isInitialized = false;
    }
  }

  isConnected() {
    return this.isInitialized;
  }

  async uploadFile(fileBuffer, filename = null) {
    if (!this.isConnected()) {
      throw new Error('IPFS service not connected');
    }

    try {
      const formData = new FormData();
      formData.append('file', fileBuffer, filename || 'file');

      const response = await axios.post(`${this.apiUrl}/add`, formData, {
        headers: {
          ...formData.getHeaders(),
        },
        timeout: 60000
      });

      const hash = response.data.Hash;
      console.log(`📁 File uploaded to IPFS: ${hash}`);

      return {
        hash,
        url: `${this.gatewayUrl}/ipfs/${hash}`,
        size: parseInt(response.data.Size)
      };
    } catch (error) {
      console.error('❌ Failed to upload file to IPFS:', error);
      throw new Error('Failed to upload file to IPFS');
    }
  }

  async uploadMultipleFiles(files) {
    if (!this.isConnected()) {
      throw new Error('IPFS service not connected');
    }

    try {
      const results = [];

      for (const file of files) {
        const result = await this.uploadFile(file.buffer, file.filename);
        results.push({
          filename: file.filename,
          originalname: file.originalname,
          ...result
        });
      }

      return results;
    } catch (error) {
      console.error('❌ Failed to upload multiple files to IPFS:', error);
      throw new Error('Failed to upload files to IPFS');
    }
  }

  async uploadJSON(jsonData, filename = null) {
    if (!this.isConnected()) {
      throw new Error('IPFS service not connected');
    }

    try {
      const jsonString = JSON.stringify(jsonData, null, 2);
      const buffer = Buffer.from(jsonString, 'utf8');

      return await this.uploadFile(buffer, filename);
    } catch (error) {
      console.error('❌ Failed to upload JSON to IPFS:', error);
      throw new Error('Failed to upload JSON to IPFS');
    }
  }

  async downloadFile(hash) {
    if (!this.isConnected()) {
      throw new Error('IPFS service not connected');
    }

    try {
      const response = await axios.post(`${this.apiUrl}/cat`, null, {
        params: { arg: hash },
        responseType: 'arraybuffer',
        timeout: 60000
      });

      const fileBuffer = Buffer.from(response.data);
      console.log(`📥 File downloaded from IPFS: ${hash}`);

      return fileBuffer;
    } catch (error) {
      console.error('❌ Failed to download file from IPFS:', error);
      throw new Error('Failed to download file from IPFS');
    }
  }

  async downloadJSON(hash) {
    if (!this.isConnected()) {
      throw new Error('IPFS service not connected');
    }

    try {
      const fileBuffer = await this.downloadFile(hash);
      const jsonString = fileBuffer.toString('utf8');

      return JSON.parse(jsonString);
    } catch (error) {
      console.error('❌ Failed to download JSON from IPFS:', error);
      throw new Error('Failed to download JSON from IPFS');
    }
  }

  async getFileInfo(hash) {
    if (!this.isConnected()) {
      throw new Error('IPFS service not connected');
    }

    try {
      const stats = await this.client.files.stat(`/ipfs/${hash}`);

      return {
        hash,
        size: stats.size,
        type: stats.type,
        url: `${this.gatewayUrl}/ipfs/${hash}`
      };
    } catch (error) {
      console.error('❌ Failed to get file info from IPFS:', error);
      throw new Error('Failed to get file info from IPFS');
    }
  }

  async pinFile(hash) {
    if (!this.isConnected()) {
      throw new Error('IPFS service not connected');
    }

    try {
      await this.client.pin.add(hash);
      console.log(`📌 File pinned to IPFS: ${hash}`);

      return true;
    } catch (error) {
      console.error('❌ Failed to pin file to IPFS:', error);
      return false;
    }
  }

  async unpinFile(hash) {
    if (!this.isConnected()) {
      throw new Error('IPFS service not connected');
    }

    try {
      await this.client.pin.rm(hash);
      console.log(`📌 File unpinned from IPFS: ${hash}`);

      return true;
    } catch (error) {
      console.error('❌ Failed to unpin file from IPFS:', error);
      return false;
    }
  }

  async listPinnedFiles() {
    if (!this.isConnected()) {
      throw new Error('IPFS service not connected');
    }

    try {
      const pinnedFiles = [];

      for await (const { cid } of this.client.pin.ls()) {
        pinnedFiles.push(cid.toString());
      }

      return pinnedFiles;
    } catch (error) {
      console.error('❌ Failed to list pinned files:', error);
      throw new Error('Failed to list pinned files');
    }
  }

  getGatewayUrl(hash) {
    return `${this.gatewayUrl}/ipfs/${hash}`;
  }

  async saveFileLocally(hash, localPath) {
    try {
      const fileBuffer = await this.downloadFile(hash);

      // Ensure directory exists
      const dir = path.dirname(localPath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      fs.writeFileSync(localPath, fileBuffer);
      console.log(`💾 File saved locally: ${localPath}`);

      return localPath;
    } catch (error) {
      console.error('❌ Failed to save file locally:', error);
      throw new Error('Failed to save file locally');
    }
  }

  async uploadFromLocalFile(localPath, filename = null) {
    try {
      if (!fs.existsSync(localPath)) {
        throw new Error('Local file does not exist');
      }

      const fileBuffer = fs.readFileSync(localPath);
      const name = filename || path.basename(localPath);

      return await this.uploadFile(fileBuffer, name);
    } catch (error) {
      console.error('❌ Failed to upload from local file:', error);
      throw new Error('Failed to upload from local file');
    }
  }

  // Utility method to validate IPFS hash
  isValidHash(hash) {
    // Basic validation for IPFS hash (CID)
    const cidRegex = /^(Qm[1-9A-HJ-NP-Za-km-z]{44}|b[A-Za-z2-7]{58}|B[A-Z2-7]{58}|z[1-9A-HJ-NP-Za-km-z]{48}|F[0-9A-F]{50})$/;
    return cidRegex.test(hash);
  }

  // Get node information
  async getNodeInfo() {
    if (!this.isConnected()) {
      throw new Error('IPFS service not connected');
    }

    try {
      return await this.client.id();
    } catch (error) {
      console.error('❌ Failed to get IPFS node info:', error);
      throw new Error('Failed to get IPFS node info');
    }
  }
}

// Create singleton instance
const ipfsService = new IPFSService();

module.exports = ipfsService;
